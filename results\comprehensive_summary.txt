K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 266.13 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     13           0.17       True      
dog             6     29           0.60       True      
dog             12    42           1.70       True      
dog             24    58           4.46       True      
dog             48    38           5.68       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     18           0.56       True      
football        6     29           1.57       True      
football        12    67           6.40       True      
football        24    122          23.23      True      
football        48    204          70.10      True      

# PARROTS (Shape: (393, 600, 3))
parrots         3     23           0.63       True      
parrots         6     158          7.79       True      
parrots         12    134          12.39      True      
parrots         24    291          49.66      True      
parrots         48    260          80.89      True      


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
