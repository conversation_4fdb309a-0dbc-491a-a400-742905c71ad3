K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 49.55 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     12           0.18       True      
dog             6     28           0.68       True      
dog             12    19           0.85       True      
dog             24    13           1.18       True      
dog             48    14           2.48       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     8            0.29       True      
football        6     11           0.64       True      
football        12    39           3.97       True      
football        24    21           4.03       True      
football        48    28           11.05      True      

# PARROTS (Shape: (393, 600, 3))
parrots         3     11           0.43       True      
parrots         6     19           1.11       True      
parrots         12    34           3.50       True      
parrots         24    35           7.56       True      
parrots         48    31           11.26      True      


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
