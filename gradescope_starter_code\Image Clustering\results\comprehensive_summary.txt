K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 51.09 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     12           0.20       True      
dog             6     28           0.75       True      
dog             12    19           0.90       True      
dog             24    13           1.17       True      
dog             48    14           2.32       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     8            0.32       True      
football        6     11           0.71       True      
football        12    39           4.30       True      
football        24    21           4.45       True      
football        48    28           11.60      True      

# PARROTS (Shape: (393, 600, 3))
parrots         3     11           0.47       True      
parrots         6     19           1.27       True      
parrots         12    34           3.78       True      
parrots         24    35           6.77       True      
parrots         48    31           11.76      True      


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
