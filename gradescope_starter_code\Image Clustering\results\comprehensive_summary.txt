K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 51.63 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     12           0.25       True      
dog             6     28           0.80       True      
dog             12    19           0.98       True      
dog             24    13           1.25       True      
dog             48    14           2.52       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     8            0.35       True      
football        6     11           0.75       True      
football        12    39           4.61       True      
football        24    21           4.60       True      
football        48    28           11.26      True      

# PARROTS (Shape: (393, 600, 3))
parrots         3     11           0.44       True      
parrots         6     19           1.24       True      
parrots         12    34           4.03       True      
parrots         24    35           6.95       True      
parrots         48    31           11.26      True      


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
