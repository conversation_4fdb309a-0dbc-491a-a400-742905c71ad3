import numpy as np
from os.path import abspath, exists

class PoliticalBlogsClustering:
    def __init__(self):
        pass

    def find_majority_labels(self, num_clusters = 2):
        '''
        This method loads the data, performs spectral clustering  and reports the majority labels

        Inputs:
            num_clusters (int): The number of clusters to be created

        Output:
            A map with following attributes
            1. overall_mismatch_rate: <2 decimal places>
            2. mismatch_rates: [{"majority_index": <int>, "mismatch_rate": <2 decimal places>}]
        '''

        map = {
            "overall_mismatch_rate": None,
            "mismatch_rates": []
        }

        # TODO - start your implementation.
        ## It is suggested to break your code into smaller methods but not nessasary. 

        return map
