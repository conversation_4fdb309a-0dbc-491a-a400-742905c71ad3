# K-means Distance Metrics Comparison

## How to Switch Between Distance Metrics

To change the distance metric in `main.py`, modify line ~100:

```python
# Set distance metric here - change this line to switch between metrics
# 'l1' = Manhattan distance (ℓ1 norm)
# 'l2' = squared-ℓ2 norm (Euclidean distance squared)
distance_metric = 'l1'  # Change to 'l2' for squared-ℓ2 norm
```

## Performance Comparison

### Manhattan Distance (ℓ1 norm) - `distance_metric = 'l1'`
- **Total Runtime**: ~54 seconds
- **Convergence**: Generally faster convergence (fewer iterations)
- **Characteristics**: Uses median for centroid updates, more robust to outliers

| Image    | K=3 | K=6 | K=12 | K=24 | K=48 |
|----------|-----|-----|------|------|------|
| Football | 8 iter (0.35s) | 11 iter (0.75s) | 39 iter (4.61s) | 21 iter (4.60s) | 28 iter (11.26s) |
| Parrots  | 11 iter (0.44s) | 19 iter (1.24s) | 34 iter (4.03s) | 35 iter (6.95s) | 31 iter (11.26s) |
| Dog      | 12 iter (0.25s) | 28 iter (0.80s) | 19 iter (0.98s) | 13 iter (1.25s) | 14 iter (2.52s) |

### Squared-ℓ2 Norm (Euclidean) - `distance_metric = 'l2'`
- **Total Runtime**: ~293 seconds
- **Convergence**: Generally slower convergence (more iterations)
- **Characteristics**: Uses mean for centroid updates, minimizes squared distances

| Image    | K=3 | K=6 | K=12 | K=24 | K=48 |
|----------|-----|-----|------|------|------|
| Football | 18 iter (0.63s) | 29 iter (1.78s) | 67 iter (7.50s) | 122 iter (25.55s) | 204 iter (78.12s) |
| Parrots  | 23 iter (0.68s) | 158 iter (8.39s) | 134 iter (15.47s) | 291 iter (52.83s) | 260 iter (85.18s) |
| Dog      | 13 iter (0.17s) | 29 iter (0.61s) | 42 iter (1.81s) | 58 iter (4.68s) | 38 iter (6.30s) |

## Key Observations

1. **Speed**: Manhattan distance (ℓ1) converges much faster than squared-ℓ2 norm
2. **Iterations**: ℓ2 norm typically requires 2-10x more iterations to converge
3. **Runtime**: ℓ1 norm completes in ~1 minute vs ~5 minutes for ℓ2 norm
4. **Stability**: Both metrics achieve convergence for all test cases
5. **Image Quality**: Both produce high-quality compressed images (visual inspection recommended)

## Recommendations

- Use **Manhattan distance (ℓ1)** for faster experimentation and development
- Use **squared-ℓ2 norm** when the assignment specifically requires Euclidean distance
- Both metrics are mathematically valid and produce good compression results
