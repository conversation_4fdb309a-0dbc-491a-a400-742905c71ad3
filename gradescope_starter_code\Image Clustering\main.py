######################################################################
# Fast K-means Image Compression Experiments
# ISYE 6740 - Computational Data Analytics
# 
# Optimized implementation that runs all experiments in under 5 minutes
######################################################################

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os
import time
from KMeans import KMeansImpl

def load_image_as_pixels(image_path):
    """
    Load an image and convert it to the required pixel format.

    Input:
    - image_path: path to the image file

    Output:
    - pixels: numpy array where each row is a pixel with RGB values [0, 255]
    - original_shape: original image shape (height, width, channels) for reconstruction
    """

    # Load image using PIL
    img = Image.open(image_path)

    # Convert to RGB if not already
    if img.mode != 'RGB':
        img = img.convert('RGB')

    # Convert to numpy array
    img_array = np.array(img)
    original_shape = img_array.shape

    # Reshape to pixels format (each row is a pixel)
    pixels = img_array.reshape(-1, 3)

    return pixels, original_shape

def reconstruct_image(class_assignments, centroids, original_shape):
    """
    Reconstruct the compressed image from cluster assignments and centroids.

    Input:
    - class_assignments: cluster assignment for each pixel (1-based indexing)
    - centroids: k centroids with RGB values
    - original_shape: original image shape (height, width, channels)

    Output:
    - reconstructed_image: numpy array representing the reconstructed image
    """

    # Convert class assignments back to 0-based indexing
    labels = class_assignments - 1

    # Create reconstructed pixel array
    reconstructed_pixels = centroids[labels]

    # Reshape back to original image shape
    reconstructed_image = reconstructed_pixels.reshape(original_shape)

    # Ensure values are in valid range and convert to uint8
    reconstructed_image = np.clip(reconstructed_image, 0, 255).astype(np.uint8)

    return reconstructed_image

def run():
    """
    Run all K-means experiments efficiently on all three images.
    """
    
    # Define image paths
    base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
    
    image_info = {
        "football": {"path": os.path.join(base_path, "football.bmp"), "seed": 42},
        "parrots": {"path": os.path.join(base_path, "parrots.png"), "seed": 123},
        "dog": {"path": os.path.join(base_path, "dog.jpg"), "seed": 456}
    }
    
    # Define k values to test
    k_values = [3, 6, 12, 24, 48]
    
    print("Fast K-means Image Compression Experiments")
    print("=" * 60)
    print(f"K values: {k_values}")
    print(f"Images: {list(image_info.keys())}")
    
    # Check if image files exist
    for name, info in image_info.items():
        if not os.path.exists(info["path"]):
            print(f"ERROR: Image file not found: {info['path']}")
            return
        else:
            print(f"Found {name}: {info['path']}")
    
    print("\n" + "=" * 60)
    
    # Create output directory
    output_dir = "results"
    os.makedirs(output_dir, exist_ok=True)
    
    # Store all results
    all_results = {}
    total_start_time = time.time()
    
    # Process each image
    for image_name, info in image_info.items():
        print(f"\n{'='*15} PROCESSING {image_name.upper()} {'='*15}")
        
        # Load image
        image_start_time = time.time()
        pixels, original_shape = load_image_as_pixels(info["path"])
        load_time = time.time() - image_start_time
        
        print(f"Loaded {image_name}: {original_shape}, {len(pixels)} pixels ({load_time:.2f}s)")
        
        # Store results for this image
        image_results = {}

        # Create KMeans instance with fixed seed for reproducibility
        kmeans = KMeansImpl(max_iterations=1000, random_seed=info["seed"])

        # Run K-means for each k value
        for k in k_values:
            print(f"  K={k}...", end=" ", flush=True)

            k_start_time = time.time()

            # Run K-means with Manhattan distance (norm_distance=1)
            result = kmeans.compress(pixels.reshape(original_shape), k, norm_distance=1)

            k_time = time.time() - k_start_time

            # Extract results
            class_assignments = result["class"]
            centroids = result["centroid"]
            iterations = result["number_of_iterations"]
            converged = result["additional_args"]["converged"]
            distance_metric = 'l1'  # Manhattan distance
            reconstructed_image = result["img"]

            # Store results
            image_results[k] = {
                'class_assignments': class_assignments,
                'centroids': centroids,
                'iterations': iterations,
                'converged': converged,
                'distance_metric': distance_metric,
                'time_seconds': k_time,
                'reconstructed_image': reconstructed_image
            }
            
            # Save reconstructed image immediately
            output_path = os.path.join(output_dir, f"{image_name}_k{k}_reconstructed.png")
            Image.fromarray(reconstructed_image).save(output_path)
            
            print(f"{iterations} iter, {k_time:.2f}s, {'Good' if converged else 'Not Good'}, distance={distance_metric}")
        
        all_results[image_name] = (image_results, original_shape)
        
        # Save individual summary
        save_individual_summary(image_name, image_results, output_dir)
        
        image_total_time = time.time() - image_start_time
        print(f"  {image_name} total time: {image_total_time:.2f}s")
    
    total_time = time.time() - total_start_time
    
    # Create comprehensive summary
    create_comprehensive_summary(all_results, output_dir, total_time)
    
    # Create comparison plots
    create_all_comparison_plots(all_results, output_dir)
    
    print(f"\n{'='*20} ALL EXPERIMENTS COMPLETE {'='*20}")
    print(f"Total runtime: {total_time:.2f} seconds")
    print(f"All results saved to: {os.path.abspath(output_dir)}")
    
    return all_results


def save_individual_summary(image_name, results, output_dir):
    """Save summary for individual image."""
    summary_path = os.path.join(output_dir, f"{image_name}_summary.txt")
    with open(summary_path, 'w') as f:
        f.write(f"K-means Image Compression Results for {image_name}\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("K\tIterations\tTime (s)\tConverged\n")
        f.write("-" * 40 + "\n")
        
        for k in sorted(results.keys()):
            result = results[k]
            f.write(f"{k}\t{result['iterations']}\t\t{result['time_seconds']:.2f}\t\t{result['converged']}\n")


def create_comprehensive_summary(all_results, output_dir, total_time):
    """Create comprehensive summary of all experiments."""
    summary_path = os.path.join(output_dir, "comprehensive_summary.txt")
    
    with open(summary_path, 'w') as f:
        f.write("K-means Image Compression - Comprehensive Results\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Total Runtime: {total_time:.2f} seconds\n\n")
        
        # Table header
        f.write(f"{'Image':<15} {'K':<5} {'Iterations':<12} {'Time (s)':<10} {'Converged':<10}\n")
        f.write("-" * 60 + "\n")
        
        # Data rows
        for image_name in sorted(all_results.keys()):
            results, original_shape = all_results[image_name]
            
            f.write(f"\n# {image_name.upper()} (Shape: {original_shape})\n")
            
            for k in sorted(results.keys()):
                result = results[k]
                f.write(f"{image_name:<15} {k:<5} {result['iterations']:<12} "
                       f"{result['time_seconds']:<10.2f} {str(result['converged']):<10}\n")
        
        f.write(f"\n\nDeliverables Generated:\n")
        f.write("1. Reconstructed images for every K on all 3 images\n")
        f.write("2. Time in seconds for convergence for every K on each image\n")
        f.write("3. Number of iterations to convergence for every K on each image\n")
        f.write("4. Comparison plots showing original vs reconstructed images\n")
    
    print(f"Comprehensive summary saved: {summary_path}")


def create_all_comparison_plots(all_results, output_dir):
    """Create comparison plots for all images."""
    print("\nCreating comparison plots...")
    
    for image_name, (results, original_shape) in all_results.items():
        # Load original image
        base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
        image_paths = {
            "football": os.path.join(base_path, "football.bmp"),
            "parrots": os.path.join(base_path, "parrots.png"),
            "dog": os.path.join(base_path, "dog.jpg")
        }
        
        original_pixels, _ = load_image_as_pixels(image_paths[image_name])
        original_image = original_pixels.reshape(original_shape)
        
        # Create subplot figure
        k_values = sorted(results.keys())
        n_plots = len(k_values) + 1  # +1 for original
        
        # Calculate subplot layout
        n_cols = 3
        n_rows = (n_plots + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        # Plot original image
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Plot reconstructed images
        plot_idx = 1
        for k in k_values:
            row = plot_idx // n_cols
            col = plot_idx % n_cols
            
            axes[row, col].imshow(results[k]['reconstructed_image'])
            axes[row, col].set_title(f'K={k} ({results[k]["iterations"]} iter, {results[k]["time_seconds"]:.2f}s)')
            axes[row, col].axis('off')
            
            plot_idx += 1
        
        # Hide unused subplots
        for i in range(plot_idx, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].axis('off')
        
        plt.tight_layout()
        
        # Save comparison plot
        comparison_path = os.path.join(output_dir, f"{image_name}_comparison.png")
        plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"  Saved: {comparison_path}")


def print_final_summary(all_results, total_time):
    """Print final summary to console."""
    print("\n" + "=" * 80)
    print("FINAL EXPERIMENT RESULTS SUMMARY")
    print("=" * 80)
    print(f"Total Runtime: {total_time:.2f} seconds")
    print()
    
    for image_name, (results, original_shape) in all_results.items():
        print(f"{image_name.upper()} (Shape: {original_shape})")
        print("-" * 50)
        print(f"{'K':<5} {'Iterations':<12} {'Time (s)':<10} {'Converged':<10}")
        print("-" * 50)
        
        for k in sorted(results.keys()):
            result = results[k]
            print(f"{k:<5} {result['iterations']:<12} {result['time_seconds']:<10.2f} {str(result['converged']):<10}")
        print()


if __name__ == "__main__":
    try:
        start_time = time.time()
        all_results = run()
        total_time = time.time() - start_time
        
        print_final_summary(all_results, total_time)
        
        if total_time < 300:  # 5 minutes
            print(f"SUCCESS: Completed in {total_time:.2f}s (under 5 minutes!)")
        else:
            print(f"WARNING: Took {total_time:.2f}s (over 5 minutes)")
        
    except Exception as e:
        print(f"\nEXPERIMENT FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
