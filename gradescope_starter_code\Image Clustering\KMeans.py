from PIL import Image
import numpy as np
import time

class KMeansImpl:
    def __init__(self, max_iterations=100, tolerance=1e-4, random_seed=None):
        """
        Initialize KMeans implementation.

        Parameters:
            max_iterations: Maximum number of iterations (default: 100)
            tolerance: Convergence tolerance (default: 1e-4)
            random_seed: Random seed for reproducibility (default: None)
        """
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.random_seed = random_seed

    def load_image(self, image_name="1.jpeg"):
        """
        Returns the image numpy array.
        It is important that image_name parameter defaults to the choice image name.
        """
        return np.array(Image.open(image_name))

    def compress(self, pixels, num_clusters, norm_distance=2):
        """
        Compress the image using K-Means clustering.

        Parameters:
            pixels: 3D image for each channel (a, b, 3), values range from 0 to 255.
            num_clusters: Number of clusters (k) to use for compression.
            norm_distance: Type of distance metric to use for clustering.
                            Can be 1 for Manhattan distance or 2 for Euclidean distance.
                            Default is 2 (Euclidean).

        Returns:
            Dictionary containing:
                "class": Cluster assignments for each pixel.
                "centroid": Locations of the cluster centroids.
                "img": Compressed image with each pixel assigned to its closest cluster.
                "number_of_iterations": total iterations taken by algorithm
                "time_taken": time taken by the compression algorithm
        """
        start_time = time.time()

        # Convert 3D image to 2D pixel array if needed
        original_shape = pixels.shape
        if len(pixels.shape) == 3:
            pixels_2d = pixels.reshape(-1, 3)
        else:
            pixels_2d = pixels

        # Run K-means clustering
        class_assignments, centroids, iterations, converged = self._kmeans_clustering(
            pixels_2d, num_clusters, norm_distance
        )

        # Reconstruct the compressed image
        compressed_image = self._reconstruct_image(class_assignments, centroids, original_shape)

        end_time = time.time()

        result_map = {
            "class": class_assignments,
            "centroid": centroids,
            "img": compressed_image,
            "number_of_iterations": iterations,
            "time_taken": end_time - start_time,
            "additional_args": {
                "converged": converged,
                "norm_distance": norm_distance
            }
        }

        return result_map

    def _kmeans_clustering(self, pixels, k, norm_distance):
        """
        Core K-means clustering algorithm.

        Parameters:
            pixels: 2D numpy array where each row is a pixel with RGB values
            k: Number of clusters
            norm_distance: 1 for Manhattan distance, 2 for Euclidean distance

        Returns:
            class_assignments: 1-based cluster assignments
            centroids: Final centroid locations
            iterations: Number of iterations until convergence
            converged: Whether the algorithm converged
        """
        if self.random_seed is not None:
            np.random.seed(self.random_seed)

        n_samples, n_features = pixels.shape
        pixels_float = pixels.astype(np.float64)

        # Initialize centroids randomly from the data points
        centroid_indices = np.random.choice(n_samples, k, replace=False)
        centroids = pixels_float[centroid_indices].copy()

        # Pre-allocate arrays for efficiency
        prev_labels = np.zeros(n_samples, dtype=np.int32)
        prev_centroids = centroids.copy()

        for iteration in range(self.max_iterations):
            # Calculate distances using broadcasting
            if norm_distance == 2:
                # Squared L2 norm (Euclidean distance squared)
                distances = np.sum((pixels_float[:, np.newaxis, :] - centroids[np.newaxis, :, :]) ** 2, axis=2)
            elif norm_distance == 1:
                # Manhattan distance (L1 norm)
                distances = np.sum(np.abs(pixels_float[:, np.newaxis, :] - centroids[np.newaxis, :, :]), axis=2)
            else:
                raise ValueError("norm_distance must be 1 (Manhattan) or 2 (Euclidean)")

            # Find closest centroid for each pixel
            labels = np.argmin(distances, axis=1)

            # Check for convergence (no label changes)
            if iteration > 0 and np.array_equal(labels, prev_labels):
                converged = True
                break

            prev_labels = labels.copy()

            # Update centroids
            for i in range(k):
                mask = (labels == i)
                if np.any(mask):
                    if norm_distance == 1:
                        # For Manhattan distance, median minimizes the sum of absolute deviations
                        centroids[i] = np.median(pixels_float[mask], axis=0)
                    else:
                        # For L2 norm, mean minimizes the sum of squared deviations
                        centroids[i] = np.mean(pixels_float[mask], axis=0)

            # Early convergence check based on centroid movement
            if iteration > 0:
                centroid_shift = np.sum((centroids - prev_centroids) ** 2)
                if centroid_shift < self.tolerance:
                    converged = True
                    break

            prev_centroids = centroids.copy()
        else:
            converged = False

        # Convert labels to 1-based indexing as required
        class_assignments = labels + 1

        # Ensure centroids are in valid range [0, 255]
        centroids = np.clip(centroids, 0, 255)

        return class_assignments, centroids, iteration + 1, converged

    def _reconstruct_image(self, class_assignments, centroids, original_shape):
        """
        Reconstruct the compressed image from cluster assignments and centroids.

        Parameters:
            class_assignments: 1-based cluster assignments for each pixel
            centroids: Cluster centroids with RGB values
            original_shape: Original image shape (height, width, channels)

        Returns:
            reconstructed_image: Numpy array representing the reconstructed image
        """
        # Convert class assignments back to 0-based indexing
        labels = class_assignments - 1

        # Create reconstructed pixel array
        reconstructed_pixels = centroids[labels]

        # Reshape back to original image shape
        if len(original_shape) == 3:
            reconstructed_image = reconstructed_pixels.reshape(original_shape)
        else:
            reconstructed_image = reconstructed_pixels

        # Ensure values are in valid range and convert to uint8
        reconstructed_image = np.clip(reconstructed_image, 0, 255).astype(np.uint8)

        return reconstructed_image
